import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/verify_two_factor_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state and operations
///
/// This BLoC handles all authentication-related business logic including:
/// - Standard email/password login
/// - Biometric authentication
/// - Two-factor authentication
/// - Logout operations
/// - Authentication status checking
/// - Token refresh
///
/// The BLoC uses domain layer use cases to perform authentication operations
/// and maintains the current authentication state of the application.
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final BiometricLoginUseCase _biometricLoginUseCase;
  final LogoutUseCase _logoutUseCase;
  final VerifyTwoFactorUseCase _verifyTwoFactorUseCase;
  final AuthRepository _authRepository;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required BiometricLoginUseCase biometricLoginUseCase,
    required LogoutUseCase logoutUseCase,
    required VerifyTwoFactorUseCase verifyTwoFactorUseCase,
    required AuthRepository authRepository,
  })  : _loginUseCase = loginUseCase,
        _biometricLoginUseCase = biometricLoginUseCase,
        _logoutUseCase = logoutUseCase,
        _verifyTwoFactorUseCase = verifyTwoFactorUseCase,
        _authRepository = authRepository,
        super(AuthInitial()) {
    // Register event handlers
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthBiometricLoginRequested>(_onBiometricLoginRequested);
    on<AuthTwoFactorSubmitted>(_onTwoFactorSubmitted);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthGlobalLogoutRequested>(_onGlobalLogoutRequested);
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthTokenRefreshRequested>(_onTokenRefreshRequested);
  }

  /// Handles standard email/password login requests
  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result = await _loginUseCase.call(event.email, event.password);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
      } else if (result.requiresTwoFactor && result.twoFactorRefCode != null) {
        emit(AuthTwoFactorRequired(result.twoFactorRefCode!));
      } else {
        final errorMessage =
            result.failure?.message ?? result.error ?? 'Login failed';
        emit(AuthError(errorMessage));
      }
    } catch (e) {
      emit(AuthError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  /// Handles biometric login requests
  Future<void> _onBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result = await _biometricLoginUseCase.call(event.email);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
      } else {
        final errorMessage =
            result.failure?.message ?? result.error ?? 'Biometric login failed';
        emit(AuthError(errorMessage));
      }
    } catch (e) {
      emit(AuthError('Biometric authentication failed: ${e.toString()}'));
    }
  }

  /// Handles two-factor authentication code submission
  Future<void> _onTwoFactorSubmitted(
    AuthTwoFactorSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final result =
          await _verifyTwoFactorUseCase.call(event.refCode, event.code);

      if (result.success && result.user != null) {
        emit(AuthAuthenticated(result.user!));
      } else {
        final errorMessage = result.failure?.message ??
            result.error ??
            'Two-factor authentication failed';
        emit(AuthError(errorMessage));
      }
    } catch (e) {
      emit(AuthError('Two-factor verification failed: ${e.toString()}'));
    }
  }

  /// Handles logout requests
  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _logoutUseCase.call();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still consider the user logged out locally
      emit(AuthUnauthenticated());
    }
  }

  /// Handles global logout requests
  Future<void> _onGlobalLogoutRequested(
    AuthGlobalLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authRepository.globalLogout();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if global logout fails, we should still consider the user logged out locally
      emit(AuthUnauthenticated());
    }
  }

  /// Handles authentication status check requests
  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated) {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          emit(AuthAuthenticated(user));
        } else {
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }

  /// Handles token refresh requests
  Future<void> _onTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _authRepository.refreshToken();
      // Token refresh doesn't change the authentication state
      // The current state should remain the same
    } catch (e) {
      // If token refresh fails, the user should be logged out
      emit(AuthUnauthenticated());
    }
  }

  /// Convenience method to check if user is currently authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Convenience method to get the current authenticated user
  AuthUser? get currentUser {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      return currentState.user;
    }
    return null;
  }
}
