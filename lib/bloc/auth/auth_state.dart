import 'package:equatable/equatable.dart';
import '../../domain/entities/auth_user.dart';
import '../../core/error/auth_failures.dart';

/// Abstract base class for all authentication states
///
/// This class defines the contract for authentication states in the AuthBloc.
/// All authentication states extend this class and implement Equatable for
/// efficient state comparison and rebuilds.
abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

/// Initial state when the AuthBloc is first created
///
/// This state represents the initial condition before any authentication
/// operations have been performed. The app should check authentication
/// status when this state is active.
class AuthInitial extends AuthState {}

/// State indicating an authentication operation is in progress
///
/// This state is emitted during login, logout, two-factor verification,
/// or any other authentication operation that requires network communication
/// or processing time.
class AuthLoading extends AuthState {}

/// State indicating the user is successfully authenticated
///
/// This state contains the authenticated user's information and is emitted
/// when login is successful, two-factor authentication is completed, or
/// when checking existing authentication status returns a valid session.
class AuthAuthenticated extends AuthState {
  /// The authenticated user's information
  final AuthUser user;

  AuthAuthenticated(this.user);

  @override
  List<Object?> get props => [user];

  @override
  String toString() => 'AuthAuthenticated(user: ${user.email})';
}

/// State indicating the user is not authenticated
///
/// This state is emitted when:
/// - User logs out successfully
/// - Authentication check fails
/// - Session expires
/// - Initial authentication check returns no valid session
class AuthUnauthenticated extends AuthState {
  @override
  String toString() => 'AuthUnauthenticated()';
}

/// State indicating two-factor authentication is required
///
/// This state is emitted when initial login is successful but the user
/// has two-factor authentication enabled. The UI should navigate to
/// the two-factor code entry screen when this state is active.
class AuthTwoFactorRequired extends AuthState {
  /// Reference code for the two-factor authentication session
  /// This code is used to verify the two-factor authentication code
  final String refCode;

  AuthTwoFactorRequired(this.refCode);

  @override
  List<Object?> get props => [refCode];

  @override
  String toString() => 'AuthTwoFactorRequired(refCode: $refCode)';
}

/// State indicating an authentication error has occurred
///
/// This state is emitted when any authentication operation fails,
/// including login failures, network errors, invalid credentials,
/// or two-factor authentication failures.
class AuthError extends AuthState {
  /// The authentication failure that occurred
  final AuthFailure failure;

  AuthError(this.failure);

  @override
  List<Object?> get props => [failure];

  @override
  String toString() => 'AuthError(failure: $failure)';
}
