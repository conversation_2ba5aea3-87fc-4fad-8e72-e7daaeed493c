import 'auth_exceptions.dart';
import 'auth_failures.dart';
import '../../view_model/custom_exception.dart';

/// Utility class for mapping data layer exceptions to domain layer failures
/// 
/// This class provides a centralized way to convert technical exceptions
/// from the data layer into user-friendly failures for the domain layer.
/// It ensures consistent error handling across the authentication module.
class ErrorMapper {
  /// Maps an exception to an appropriate authentication failure
  /// 
  /// This method handles the conversion of various exception types to
  /// corresponding failure types, providing user-friendly error messages
  /// while preserving technical details for debugging.
  /// 
  /// Parameters:
  /// - [exception]: The exception to map to a failure
  /// 
  /// Returns: An [AuthFailure] appropriate for the given exception
  static AuthFailure mapExceptionToFailure(dynamic exception) {
    // Handle authentication-specific exceptions
    if (exception is AuthException) {
      return _mapAuthException(exception);
    }
    
    // Handle legacy CustomException
    if (exception is CustomException) {
      return _mapCustomException(exception);
    }
    
    // Handle common Dart exceptions
    if (exception is FormatException) {
      return const ValidationFailure(
        field: 'input',
        message: 'Invalid input format. Please check your data and try again',
      );
    }
    
    // Handle timeout exceptions
    if (exception.toString().toLowerCase().contains('timeout')) {
      return const TimeoutFailure();
    }
    
    // Handle network-related exceptions
    if (_isNetworkException(exception)) {
      return const NetworkFailure();
    }
    
    // Default to unknown failure for unhandled exceptions
    return UnknownFailure(
      message: 'An unexpected error occurred. Please try again',
      context: {'originalException': exception.toString()},
    );
  }

  /// Maps authentication-specific exceptions to failures
  static AuthFailure _mapAuthException(AuthException exception) {
    switch (exception.runtimeType) {
      case NetworkException:
        return NetworkFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case ServerException:
        return ServerFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case InvalidCredentialsException:
        return InvalidCredentialsFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case AccountNotVerifiedException:
        final accountException = exception as AccountNotVerifiedException;
        return AccountNotVerifiedFailure(
          verificationType: accountException.verificationType,
          message: exception.message,
          context: exception.context,
        );
        
      case InvalidTwoFactorCodeException:
        return InvalidTwoFactorCodeFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case TwoFactorCodeExpiredException:
        return TwoFactorCodeExpiredFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricException:
        return _mapBiometricException(exception as BiometricException);
        
      case InvalidTokenException:
      case RefreshTokenExpiredException:
        return SessionExpiredFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case TokenException:
        return TokenFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case InvalidEmailFormatException:
        return InvalidEmailFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case InvalidPasswordFormatException:
        return InvalidPasswordFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case ValidationException:
        final validationException = exception as ValidationException;
        return ValidationFailure(
          field: validationException.field,
          message: exception.message,
          context: exception.context,
        );
        
      case TimeoutException:
        return TimeoutFailure(
          message: exception.message,
          context: exception.context,
        );
        
      default:
        return UnknownFailure(
          message: exception.message,
          context: exception.context,
        );
    }
  }

  /// Maps biometric exceptions to appropriate failures
  static AuthFailure _mapBiometricException(BiometricException exception) {
    switch (exception.failureType) {
      case BiometricFailureType.notAvailable:
        return BiometricNotAvailableFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.notEnrolled:
        return BiometricNotEnrolledFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.authenticationFailed:
        return BiometricAuthenticationFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.userCancelled:
        return BiometricCancelledFailure(
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.lockedOut:
        return BiometricLockedFailure(
          isPermanent: false,
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.permanentlyLockedOut:
        return BiometricLockedFailure(
          isPermanent: true,
          message: exception.message,
          context: exception.context,
        );
        
      case BiometricFailureType.noStoredCredentials:
        return NoStoredCredentialsFailure(
          message: exception.message,
          context: exception.context,
        );
    }
  }

  /// Maps legacy CustomException to appropriate failures
  static AuthFailure _mapCustomException(CustomException exception) {
    final message = exception.toString();
    final lowerMessage = message.toLowerCase();
    
    // Map based on common error messages
    if (lowerMessage.contains('incorrect email or password') ||
        lowerMessage.contains('invalid credentials')) {
      return const InvalidCredentialsFailure();
    }
    
    if (lowerMessage.contains('email') && lowerMessage.contains('unverified')) {
      return const AccountNotVerifiedFailure(verificationType: 'email');
    }
    
    if (lowerMessage.contains('phone') && lowerMessage.contains('unverified')) {
      return const AccountNotVerifiedFailure(verificationType: 'phone');
    }
    
    if (lowerMessage.contains('no internet') || 
        lowerMessage.contains('network') ||
        lowerMessage.contains('connection')) {
      return const NetworkFailure();
    }
    
    if (lowerMessage.contains('server') || 
        lowerMessage.contains('http')) {
      return const ServerFailure();
    }
    
    if (lowerMessage.contains('timeout')) {
      return const TimeoutFailure();
    }
    
    if (lowerMessage.contains('biometric')) {
      return const BiometricAuthenticationFailure();
    }
    
    if (lowerMessage.contains('two-factor') || 
        lowerMessage.contains('2fa') ||
        lowerMessage.contains('verification code')) {
      return const InvalidTwoFactorCodeFailure();
    }
    
    // Default mapping for CustomException
    return UnknownFailure(
      message: 'An error occurred. Please try again',
      context: {'originalMessage': message},
    );
  }

  /// Checks if an exception is network-related
  static bool _isNetworkException(dynamic exception) {
    final exceptionString = exception.toString().toLowerCase();
    return exceptionString.contains('socketexception') ||
           exceptionString.contains('handshakeexception') ||
           exceptionString.contains('connection') ||
           exceptionString.contains('network') ||
           exceptionString.contains('no internet');
  }
}
