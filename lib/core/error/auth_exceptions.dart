/// Authentication-specific exceptions for the data layer
/// 
/// These exceptions represent technical errors that occur in the data layer
/// during authentication operations. They are caught by the repository layer
/// and mapped to appropriate domain failures.
/// 
/// All authentication exceptions extend [AuthException] for consistent handling.

/// Base class for all authentication-related exceptions
/// 
/// This abstract class provides a common interface for all authentication
/// exceptions and includes error codes for programmatic handling.
abstract class AuthException implements Exception {
  /// Human-readable error message
  final String message;
  
  /// Error code for programmatic handling
  final String code;
  
  /// Additional context information
  final Map<String, dynamic>? context;

  const AuthException({
    required this.message,
    required this.code,
    this.context,
  });

  @override
  String toString() => 'AuthException($code): $message';
}

/// Exception thrown when network connectivity issues occur
class NetworkException extends AuthException {
  const NetworkException({
    String message = 'Network connection failed',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'NETWORK_ERROR',
          context: context,
        );
}

/// Exception thrown when server returns an error response
class Server<PERSON>x<PERSON> extends AuthException {
  /// HTTP status code if applicable
  final int? statusCode;

  const ServerException({
    required String message,
    this.statusCode,
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'SERVER_ERROR',
          context: context,
        );

  @override
  String toString() => 'ServerException($code): $message${statusCode != null ? ' (HTTP $statusCode)' : ''}';
}

/// Exception thrown when authentication credentials are invalid
class InvalidCredentialsException extends AuthException {
  const InvalidCredentialsException({
    String message = 'Invalid email or password',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'INVALID_CREDENTIALS',
          context: context,
        );
}

/// Exception thrown when user account is not verified
class AccountNotVerifiedException extends AuthException {
  /// Type of verification required (email, phone, etc.)
  final String verificationType;

  const AccountNotVerifiedException({
    required this.verificationType,
    String? message,
    Map<String, dynamic>? context,
  }) : super(
          message: message ?? 'Account $verificationType verification required',
          code: 'ACCOUNT_NOT_VERIFIED',
          context: context,
        );
}

/// Exception thrown when two-factor authentication fails
class TwoFactorException extends AuthException {
  const TwoFactorException({
    required String message,
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TWO_FACTOR_ERROR',
          context: context,
        );
}

/// Exception thrown when two-factor authentication code is invalid
class InvalidTwoFactorCodeException extends TwoFactorException {
  const InvalidTwoFactorCodeException({
    String message = 'Invalid two-factor authentication code',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          context: context,
        );
}

/// Exception thrown when two-factor authentication code has expired
class TwoFactorCodeExpiredException extends TwoFactorException {
  const TwoFactorCodeExpiredException({
    String message = 'Two-factor authentication code has expired',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          context: context,
        );
}

/// Exception thrown when biometric authentication fails
class BiometricException extends AuthException {
  /// Type of biometric failure
  final BiometricFailureType failureType;

  const BiometricException({
    required this.failureType,
    String? message,
    Map<String, dynamic>? context,
  }) : super(
          message: message ?? _getDefaultMessage(failureType),
          code: 'BIOMETRIC_ERROR',
          context: context,
        );

  static String _getDefaultMessage(BiometricFailureType type) {
    switch (type) {
      case BiometricFailureType.notAvailable:
        return 'Biometric authentication is not available on this device';
      case BiometricFailureType.notEnrolled:
        return 'No biometric credentials are enrolled on this device';
      case BiometricFailureType.authenticationFailed:
        return 'Biometric authentication failed';
      case BiometricFailureType.userCancelled:
        return 'Biometric authentication was cancelled by user';
      case BiometricFailureType.lockedOut:
        return 'Biometric authentication is temporarily locked';
      case BiometricFailureType.permanentlyLockedOut:
        return 'Biometric authentication is permanently locked';
      case BiometricFailureType.noStoredCredentials:
        return 'No stored credentials found for biometric authentication';
    }
  }
}

/// Types of biometric authentication failures
enum BiometricFailureType {
  /// Biometric authentication is not available on the device
  notAvailable,
  
  /// No biometric credentials are enrolled
  notEnrolled,
  
  /// Biometric authentication failed (e.g., fingerprint not recognized)
  authenticationFailed,
  
  /// User cancelled the biometric authentication
  userCancelled,
  
  /// Biometric authentication is temporarily locked due to too many failed attempts
  lockedOut,
  
  /// Biometric authentication is permanently locked
  permanentlyLockedOut,
  
  /// No stored credentials found for biometric authentication
  noStoredCredentials,
}

/// Exception thrown when token operations fail
class TokenException extends AuthException {
  const TokenException({
    required String message,
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TOKEN_ERROR',
          context: context,
        );
}

/// Exception thrown when access token is invalid or expired
class InvalidTokenException extends TokenException {
  const InvalidTokenException({
    String message = 'Access token is invalid or expired',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          context: context,
        );
}

/// Exception thrown when refresh token is invalid or expired
class RefreshTokenExpiredException extends TokenException {
  const RefreshTokenExpiredException({
    String message = 'Refresh token has expired',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          context: context,
        );
}

/// Exception thrown when input validation fails
class ValidationException extends AuthException {
  /// Field that failed validation
  final String field;

  const ValidationException({
    required this.field,
    required String message,
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'VALIDATION_ERROR',
          context: context,
        );
}

/// Exception thrown when email format is invalid
class InvalidEmailFormatException extends ValidationException {
  const InvalidEmailFormatException({
    String message = 'Please enter a valid email address',
    Map<String, dynamic>? context,
  }) : super(
          field: 'email',
          message: message,
          context: context,
        );
}

/// Exception thrown when password format is invalid
class InvalidPasswordFormatException extends ValidationException {
  const InvalidPasswordFormatException({
    String message = 'Password does not meet requirements',
    Map<String, dynamic>? context,
  }) : super(
          field: 'password',
          message: message,
          context: context,
        );
}

/// Exception thrown when request timeout occurs
class TimeoutException extends AuthException {
  /// Timeout duration in seconds
  final int timeoutSeconds;

  const TimeoutException({
    required this.timeoutSeconds,
    String? message,
    Map<String, dynamic>? context,
  }) : super(
          message: message ?? 'Request timed out after $timeoutSeconds seconds',
          code: 'TIMEOUT_ERROR',
          context: context,
        );
}

/// Exception thrown for unexpected or unknown errors
class UnknownAuthException extends AuthException {
  /// Original exception that caused this error
  final dynamic originalException;

  const UnknownAuthException({
    required this.originalException,
    String message = 'An unexpected error occurred',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'UNKNOWN_ERROR',
          context: context,
        );

  @override
  String toString() => 'UnknownAuthException($code): $message (Original: $originalException)';
}
