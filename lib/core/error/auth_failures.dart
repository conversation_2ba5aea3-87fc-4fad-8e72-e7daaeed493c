import 'package:equatable/equatable.dart';

/// Authentication-specific failures for the domain layer
/// 
/// These failures represent business logic errors that occur during
/// authentication operations. They are returned by repository methods
/// and handled by use cases and BLoCs.
/// 
/// All authentication failures extend [AuthFailure] for consistent handling.

/// Base class for all authentication-related failures
/// 
/// This abstract class provides a common interface for all authentication
/// failures and includes error codes for programmatic handling.
abstract class AuthFailure extends Equatable {
  /// Human-readable error message for users
  final String message;
  
  /// Error code for programmatic handling
  final String code;
  
  /// Additional context information
  final Map<String, dynamic>? context;

  const AuthFailure({
    required this.message,
    required this.code,
    this.context,
  });

  @override
  List<Object?> get props => [message, code, context];

  @override
  String toString() => 'AuthFailure($code): $message';
}

/// Failure when network connectivity issues occur
class NetworkFailure extends AuthFailure {
  const NetworkFailure({
    String message = 'Please check your internet connection and try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'NETWORK_FAILURE',
          context: context,
        );
}

/// Failure when server is unavailable or returns an error
class ServerFailure extends AuthFailure {
  const ServerFailure({
    String message = 'Server is temporarily unavailable. Please try again later',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'SERVER_FAILURE',
          context: context,
        );
}

/// Failure when authentication credentials are invalid
class InvalidCredentialsFailure extends AuthFailure {
  const InvalidCredentialsFailure({
    String message = 'Invalid email or password. Please check your credentials and try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'INVALID_CREDENTIALS',
          context: context,
        );
}

/// Failure when user account requires verification
class AccountNotVerifiedFailure extends AuthFailure {
  /// Type of verification required
  final String verificationType;

  const AccountNotVerifiedFailure({
    required this.verificationType,
    String? message,
    Map<String, dynamic>? context,
  }) : super(
          message: message ?? 'Please verify your $verificationType before signing in',
          code: 'ACCOUNT_NOT_VERIFIED',
          context: context,
        );

  @override
  List<Object?> get props => [message, code, context, verificationType];
}

/// Failure when two-factor authentication is required but not provided
class TwoFactorRequiredFailure extends AuthFailure {
  /// Reference code for two-factor authentication
  final String refCode;

  const TwoFactorRequiredFailure({
    required this.refCode,
    String message = 'Two-factor authentication is required',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TWO_FACTOR_REQUIRED',
          context: context,
        );

  @override
  List<Object?> get props => [message, code, context, refCode];
}

/// Failure when two-factor authentication code is invalid
class InvalidTwoFactorCodeFailure extends AuthFailure {
  const InvalidTwoFactorCodeFailure({
    String message = 'Invalid verification code. Please check the code and try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'INVALID_TWO_FACTOR_CODE',
          context: context,
        );
}

/// Failure when two-factor authentication code has expired
class TwoFactorCodeExpiredFailure extends AuthFailure {
  const TwoFactorCodeExpiredFailure({
    String message = 'Verification code has expired. Please request a new code',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TWO_FACTOR_CODE_EXPIRED',
          context: context,
        );
}

/// Failure when biometric authentication is not available
class BiometricNotAvailableFailure extends AuthFailure {
  const BiometricNotAvailableFailure({
    String message = 'Biometric authentication is not available on this device',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'BIOMETRIC_NOT_AVAILABLE',
          context: context,
        );
}

/// Failure when biometric authentication is not set up
class BiometricNotEnrolledFailure extends AuthFailure {
  const BiometricNotEnrolledFailure({
    String message = 'Please set up biometric authentication in your device settings',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'BIOMETRIC_NOT_ENROLLED',
          context: context,
        );
}

/// Failure when biometric authentication fails
class BiometricAuthenticationFailure extends AuthFailure {
  const BiometricAuthenticationFailure({
    String message = 'Biometric authentication failed. Please try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'BIOMETRIC_AUTHENTICATION_FAILED',
          context: context,
        );
}

/// Failure when user cancels biometric authentication
class BiometricCancelledFailure extends AuthFailure {
  const BiometricCancelledFailure({
    String message = 'Biometric authentication was cancelled',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'BIOMETRIC_CANCELLED',
          context: context,
        );
}

/// Failure when biometric authentication is locked
class BiometricLockedFailure extends AuthFailure {
  /// Whether the lockout is permanent
  final bool isPermanent;

  const BiometricLockedFailure({
    this.isPermanent = false,
    String? message,
    Map<String, dynamic>? context,
  }) : super(
          message: message ?? 
            (isPermanent 
              ? 'Biometric authentication is permanently locked. Please use password login'
              : 'Biometric authentication is temporarily locked. Please try again later'),
          code: isPermanent ? 'BIOMETRIC_PERMANENTLY_LOCKED' : 'BIOMETRIC_TEMPORARILY_LOCKED',
          context: context,
        );

  @override
  List<Object?> get props => [message, code, context, isPermanent];
}

/// Failure when no stored credentials are found for biometric authentication
class NoStoredCredentialsFailure extends AuthFailure {
  const NoStoredCredentialsFailure({
    String message = 'No stored credentials found. Please sign in with your password first',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'NO_STORED_CREDENTIALS',
          context: context,
        );
}

/// Failure when token operations fail
class TokenFailure extends AuthFailure {
  const TokenFailure({
    String message = 'Authentication token error. Please sign in again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TOKEN_FAILURE',
          context: context,
        );
}

/// Failure when session has expired
class SessionExpiredFailure extends AuthFailure {
  const SessionExpiredFailure({
    String message = 'Your session has expired. Please sign in again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'SESSION_EXPIRED',
          context: context,
        );
}

/// Failure when input validation fails
class ValidationFailure extends AuthFailure {
  /// Field that failed validation
  final String field;

  const ValidationFailure({
    required this.field,
    required String message,
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'VALIDATION_FAILURE',
          context: context,
        );

  @override
  List<Object?> get props => [message, code, context, field];
}

/// Failure when email format is invalid
class InvalidEmailFailure extends ValidationFailure {
  const InvalidEmailFailure({
    String message = 'Please enter a valid email address',
    Map<String, dynamic>? context,
  }) : super(
          field: 'email',
          message: message,
          context: context,
        );
}

/// Failure when password format is invalid
class InvalidPasswordFailure extends ValidationFailure {
  const InvalidPasswordFailure({
    String message = 'Password does not meet requirements',
    Map<String, dynamic>? context,
  }) : super(
          field: 'password',
          message: message,
          context: context,
        );
}

/// Failure when request times out
class TimeoutFailure extends AuthFailure {
  const TimeoutFailure({
    String message = 'Request timed out. Please check your connection and try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'TIMEOUT_FAILURE',
          context: context,
        );
}

/// Failure for unexpected or unknown errors
class UnknownFailure extends AuthFailure {
  const UnknownFailure({
    String message = 'An unexpected error occurred. Please try again',
    Map<String, dynamic>? context,
  }) : super(
          message: message,
          code: 'UNKNOWN_FAILURE',
          context: context,
        );
}
