import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';

// Domain layer imports
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/verify_two_factor_usecase.dart';

// Data layer imports
import '../../data/datasources/auth_local_datasource.dart';
import '../../data/datasources/auth_remote_datasource.dart';
import '../../data/repositories/auth_repository_impl.dart';

// BLoC imports
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/dashboardBloc/dashboardBloc.dart';

/// Service locator instance
final sl = GetIt.instance;

/// Initialize dependency injection container
///
/// This function sets up all the dependencies required for the application
/// following the dependency injection pattern. Dependencies are registered
/// in the correct order to ensure all requirements are met.
///
/// Registration order:
/// 1. External dependencies (third-party packages)
/// 2. Use cases (domain layer)
/// 3. Repositories (domain abstractions and implementations)
/// 4. BLoCs (presentation layer)
Future<void> init() async {
  //! External dependencies
  // Register third-party packages that don't depend on our code
  sl.registerLazySingleton(() => const FlutterSecureStorage());
  sl.registerLazySingleton(() => http.Client());
  sl.registerLazySingleton(() => LocalAuthentication());

  //! Data sources
  // Register data sources as lazy singletons since they handle data operations
  // and can be reused across the application
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(
      secureStorage: sl(),
      localAuth: sl(),
    ),
  );

  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      httpClient: sl(),
    ),
  );

  //! Use cases
  // Register domain use cases as lazy singletons since they contain business logic
  // and don't need to be recreated for each use
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => BiometricLoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => VerifyTwoFactorUseCase(sl()));

  //! Repositories
  // Register repository implementation using the new data layer
  // This implementation coordinates between remote and local data sources
  // following clean architecture principles
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );

  //! BLoCs
  // Register BLoCs as factories since they manage state and should be
  // recreated when needed to avoid state pollution
  sl.registerFactory(() => AuthBloc(
        loginUseCase: sl(),
        biometricLoginUseCase: sl(),
        logoutUseCase: sl(),
        verifyTwoFactorUseCase: sl(),
        authRepository: sl(),
      ));

  sl.registerFactory(() => DashboardBloc(authBloc: sl()));
}
