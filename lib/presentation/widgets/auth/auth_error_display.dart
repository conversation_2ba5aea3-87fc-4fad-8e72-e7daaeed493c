import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../../core/error/auth_failures.dart';
import '../../../theme/theme2.dart';
import '../../../utils/alert_message.dart';

/// A reusable widget for displaying authentication errors in a consistent manner
/// 
/// This widget takes an [AuthFailure] object and displays it using the app's
/// standard error display pattern. It provides user-friendly error messages
/// and actionable guidance based on the specific failure type.
class AuthErrorDisplay extends StatelessWidget {
  /// The authentication failure to display
  final AuthFailure failure;
  
  /// Optional callback for retry actions
  final VoidCallback? onRetry;
  
  /// Optional callback for alternative actions (e.g., "Use Password" for biometric failures)
  final VoidCallback? onAlternativeAction;
  
  /// Text for the alternative action button
  final String? alternativeActionText;

  const AuthErrorDisplay({
    Key? key,
    required this.failure,
    this.onRetry,
    this.onAlternativeAction,
    this.alternativeActionText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<ThemeNotifier>(context).currentTheme;
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: theme.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.errorColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                _getErrorIcon(),
                color: theme.errorColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  failure.message,
                  style: GoogleFonts.roboto(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: theme.basicAdvanceTextColor,
                  ),
                ),
              ),
            ],
          ),
          if (_shouldShowActions()) ...[
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (onAlternativeAction != null && alternativeActionText != null) ...[
                  TextButton(
                    onPressed: onAlternativeAction,
                    child: Text(
                      alternativeActionText!,
                      style: GoogleFonts.roboto(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: theme.primaryColor,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                ],
                if (onRetry != null && _shouldShowRetry())
                  TextButton(
                    onPressed: onRetry,
                    child: Text(
                      'Try Again',
                      style: GoogleFonts.roboto(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: theme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Returns the appropriate icon for the error type
  IconData _getErrorIcon() {
    switch (failure.runtimeType) {
      case NetworkFailure:
        return Icons.wifi_off;
      case ServerFailure:
        return Icons.cloud_off;
      case InvalidCredentialsFailure:
        return Icons.lock_outline;
      case BiometricNotAvailableFailure:
      case BiometricAuthenticationFailure:
      case BiometricCancelledFailure:
      case BiometricLockedFailure:
        return Icons.fingerprint;
      case AccountNotVerifiedFailure:
        return Icons.email_outlined;
      case InvalidTwoFactorCodeFailure:
        return Icons.security;
      case ValidationFailure:
        return Icons.warning_outlined;
      case TimeoutFailure:
        return Icons.access_time;
      case SessionExpiredFailure:
        return Icons.schedule;
      default:
        return Icons.error_outline;
    }
  }

  /// Determines if action buttons should be shown
  bool _shouldShowActions() {
    return onRetry != null || (onAlternativeAction != null && alternativeActionText != null);
  }

  /// Determines if the retry button should be shown for this error type
  bool _shouldShowRetry() {
    switch (failure.runtimeType) {
      case NetworkFailure:
      case ServerFailure:
      case TimeoutFailure:
      case BiometricAuthenticationFailure:
      case InvalidTwoFactorCodeFailure:
        return true;
      case BiometricCancelledFailure:
        return true;
      case BiometricLockedFailure:
        final lockedFailure = failure as BiometricLockedFailure;
        return !lockedFailure.isPermanent;
      default:
        return false;
    }
  }
}

/// Utility class for showing authentication errors using the app's standard alert system
/// 
/// This class provides static methods to display authentication errors consistently
/// across the app using both the existing CustomAlert system and the new AuthErrorDisplay widget.
class AuthErrorHandler {
  /// Shows an authentication error using the standard scaffold messenger
  /// 
  /// This method displays the error using the existing CustomAlert system
  /// for consistency with the current app design.
  static void showError(BuildContext context, AuthFailure failure) {
    CustomAlert.showCustomScaffoldMessenger(
      context,
      failure.message,
      AlertType.error,
    );
  }

  /// Shows an authentication error with retry and alternative action options
  /// 
  /// This method displays a more detailed error widget that can include
  /// action buttons for retry or alternative authentication methods.
  static void showErrorWithActions(
    BuildContext context,
    AuthFailure failure, {
    VoidCallback? onRetry,
    VoidCallback? onAlternativeAction,
    String? alternativeActionText,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: AuthErrorDisplay(
          failure: failure,
          onRetry: onRetry != null ? () {
            Navigator.of(context).pop();
            onRetry();
          } : null,
          onAlternativeAction: onAlternativeAction != null ? () {
            Navigator.of(context).pop();
            onAlternativeAction();
          } : null,
          alternativeActionText: alternativeActionText,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Gets user-friendly alternative action text based on the failure type
  static String? getAlternativeActionText(AuthFailure failure) {
    switch (failure.runtimeType) {
      case BiometricNotAvailableFailure:
      case BiometricAuthenticationFailure:
      case BiometricCancelledFailure:
      case BiometricLockedFailure:
      case NoStoredCredentialsFailure:
        return 'Use Password';
      case AccountNotVerifiedFailure:
        return 'Resend Verification';
      case SessionExpiredFailure:
        return 'Sign In Again';
      default:
        return null;
    }
  }
}
