import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import '../models/auth_session_model.dart';
import '../models/auth_user_model.dart';

/// Abstract interface for local authentication data operations
abstract class AuthLocalDataSource {
  /// Session management
  Future<void> saveSession(AuthSessionModel session);
  Future<AuthSessionModel?> getSession();
  Future<void> clearSession();
  
  /// Token management
  Future<void> saveTokens(String accessToken, String refreshToken);
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<void> clearTokens();
  
  /// User data management
  Future<void> saveUser(AuthUserModel user);
  Future<AuthUserModel?> getUser();
  Future<void> clearUser();
  
  /// Credentials management for biometric login
  Future<void> saveCredentials(String email, String password);
  Future<Map<String, String>?> getCredentials();
  Future<void> clearCredentials();
  
  /// Biometric authentication
  Future<bool> isBiometricAvailable();
  Future<bool> isBiometricEnabled();
  Future<void> setBiometricEnabled(bool enabled);
  Future<bool> authenticateWithBiometric({String? reason});
  
  /// Two-factor authentication settings
  Future<void> setTwoFactorEnabled(bool enabled);
  Future<bool> isTwoFactorEnabled();
  
  /// Theme and app settings
  Future<void> saveThemeMode(String themeMode);
  Future<String?> getThemeMode();
  
  /// Clear all stored data
  Future<void> clearAllData();
}

/// Implementation of AuthLocalDataSource using FlutterSecureStorage and LocalAuthentication
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorage secureStorage;
  final LocalAuthentication localAuth;

  // Storage keys
  static const String _sessionKey = 'auth_session';
  static const String _userKey = 'auth_user';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _emailKey = 'email';
  static const String _passwordKey = 'password';
  static const String _biometricKey = 'biometric';
  static const String _twoFactorKey = 'two_factor';
  static const String _themeModeKey = 'themeMode';

  const AuthLocalDataSourceImpl({
    required this.secureStorage,
    required this.localAuth,
  });

  @override
  Future<void> saveSession(AuthSessionModel session) async {
    try {
      final sessionJson = json.encode(session.toJson());
      await secureStorage.write(key: _sessionKey, value: sessionJson);
    } catch (e) {
      throw Exception('Failed to save session: $e');
    }
  }

  @override
  Future<AuthSessionModel?> getSession() async {
    try {
      final sessionJson = await secureStorage.read(key: _sessionKey);
      if (sessionJson == null) return null;
      
      final sessionMap = json.decode(sessionJson) as Map<String, dynamic>;
      return AuthSessionModel.fromJson(sessionMap);
    } catch (e) {
      // If we can't parse the session, return null
      return null;
    }
  }

  @override
  Future<void> clearSession() async {
    try {
      await secureStorage.delete(key: _sessionKey);
    } catch (e) {
      // Ignore errors when clearing
    }
  }

  @override
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    try {
      await Future.wait([
        secureStorage.write(key: _accessTokenKey, value: accessToken),
        secureStorage.write(key: _refreshTokenKey, value: refreshToken),
      ]);
    } catch (e) {
      throw Exception('Failed to save tokens: $e');
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      return await secureStorage.read(key: _accessTokenKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearTokens() async {
    try {
      await Future.wait([
        secureStorage.delete(key: _accessTokenKey),
        secureStorage.delete(key: _refreshTokenKey),
      ]);
    } catch (e) {
      // Ignore errors when clearing
    }
  }

  @override
  Future<void> saveUser(AuthUserModel user) async {
    try {
      final userJson = json.encode(user.toJson());
      await secureStorage.write(key: _userKey, value: userJson);
    } catch (e) {
      throw Exception('Failed to save user: $e');
    }
  }

  @override
  Future<AuthUserModel?> getUser() async {
    try {
      final userJson = await secureStorage.read(key: _userKey);
      if (userJson == null) return null;
      
      final userMap = json.decode(userJson) as Map<String, dynamic>;
      return AuthUserModel.fromJson(userMap);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearUser() async {
    try {
      await secureStorage.delete(key: _userKey);
    } catch (e) {
      // Ignore errors when clearing
    }
  }

  @override
  Future<void> saveCredentials(String email, String password) async {
    try {
      await Future.wait([
        secureStorage.write(key: _emailKey, value: email),
        secureStorage.write(key: _passwordKey, value: password),
      ]);
    } catch (e) {
      throw Exception('Failed to save credentials: $e');
    }
  }

  @override
  Future<Map<String, String>?> getCredentials() async {
    try {
      final email = await secureStorage.read(key: _emailKey);
      final password = await secureStorage.read(key: _passwordKey);
      
      if (email == null || password == null) return null;
      
      return {'email': email, 'password': password};
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearCredentials() async {
    try {
      await Future.wait([
        secureStorage.delete(key: _emailKey),
        secureStorage.delete(key: _passwordKey),
      ]);
    } catch (e) {
      // Ignore errors when clearing
    }
  }

  @override
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await localAuth.canCheckBiometrics;
      if (!isAvailable) return false;
      
      final availableBiometrics = await localAuth.getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      final biometricValue = await secureStorage.read(key: _biometricKey);
      return biometricValue == 'true';
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await secureStorage.write(key: _biometricKey, value: enabled.toString());
    } catch (e) {
      throw Exception('Failed to set biometric enabled: $e');
    }
  }

  @override
  Future<bool> authenticateWithBiometric({String? reason}) async {
    try {
      if (!await isBiometricAvailable()) return false;
      
      return await localAuth.authenticate(
        localizedReason: reason ?? 'Please authenticate to proceed',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
          useErrorDialogs: true,
        ),
      );
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setTwoFactorEnabled(bool enabled) async {
    try {
      await secureStorage.write(key: _twoFactorKey, value: enabled.toString());
    } catch (e) {
      throw Exception('Failed to set two factor enabled: $e');
    }
  }

  @override
  Future<bool> isTwoFactorEnabled() async {
    try {
      final twoFactorValue = await secureStorage.read(key: _twoFactorKey);
      return twoFactorValue == 'true';
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> saveThemeMode(String themeMode) async {
    try {
      await secureStorage.write(key: _themeModeKey, value: themeMode);
    } catch (e) {
      throw Exception('Failed to save theme mode: $e');
    }
  }

  @override
  Future<String?> getThemeMode() async {
    try {
      return await secureStorage.read(key: _themeModeKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAllData() async {
    try {
      await secureStorage.deleteAll();
    } catch (e) {
      // Ignore errors when clearing all data
    }
  }
}
