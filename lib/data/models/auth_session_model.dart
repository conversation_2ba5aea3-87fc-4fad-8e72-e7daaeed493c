import 'package:json_annotation/json_annotation.dart';

part 'auth_session_model.g.dart';

/// Data model for authentication session with JSON serialization
/// 
/// This model handles token storage, expiration tracking, and session management.
/// It provides methods to check token validity and manage session state.
@JsonSerializable()
class AuthSessionModel {
  /// JWT access token for API authentication
  final String accessToken;
  
  /// Refresh token for obtaining new access tokens
  final String refreshToken;
  
  /// Timestamp when the session was created (milliseconds since epoch)
  final int createdAt;
  
  /// Timestamp when the session was last updated (milliseconds since epoch)
  final int lastUpdated;
  
  /// Whether two-factor authentication is enabled for this session
  final bool twoFactorEnabled;
  
  /// User's email associated with this session
  final String? email;
  
  /// Whether biometric authentication is enabled for this session
  final bool biometricEnabled;

  const AuthSessionModel({
    required this.accessToken,
    required this.refreshToken,
    required this.createdAt,
    required this.lastUpdated,
    this.twoFactorEnabled = false,
    this.email,
    this.biometricEnabled = false,
  });

  /// Creates an AuthSessionModel from JSON
  factory AuthSessionModel.fromJson(Map<String, dynamic> json) => 
      _$AuthSessionModelFromJson(json);

  /// Converts AuthSessionModel to JSON
  Map<String, dynamic> toJson() => _$AuthSessionModelToJson(this);

  /// Creates a new session model with updated tokens
  AuthSessionModel copyWithTokens({
    required String accessToken,
    required String refreshToken,
  }) {
    return AuthSessionModel(
      accessToken: accessToken,
      refreshToken: refreshToken,
      createdAt: createdAt,
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      twoFactorEnabled: twoFactorEnabled,
      email: email,
      biometricEnabled: biometricEnabled,
    );
  }

  /// Creates a new session model with updated settings
  AuthSessionModel copyWith({
    String? accessToken,
    String? refreshToken,
    int? createdAt,
    int? lastUpdated,
    bool? twoFactorEnabled,
    String? email,
    bool? biometricEnabled,
  }) {
    return AuthSessionModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      email: email ?? this.email,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
    );
  }

  /// Checks if the access token is expiring soon (within 5 minutes)
  /// 
  /// This is a simplified check based on the existing LoginPostRequests logic.
  /// In a real implementation, you would decode the JWT and check the exp claim.
  bool get isTokenExpiring {
    try {
      // For now, we'll use a simple time-based check
      // In production, you should decode the JWT and check the actual expiration
      final now = DateTime.now().millisecondsSinceEpoch;
      final tokenAge = now - lastUpdated;
      
      // Consider token expiring if it's older than 55 minutes (assuming 1-hour tokens)
      return tokenAge > (55 * 60 * 1000);
    } catch (e) {
      return true; // If we can't determine, assume it's expiring
    }
  }

  /// Checks if the session is valid (has tokens and not expired)
  bool get isValid {
    return accessToken.isNotEmpty && 
           refreshToken.isNotEmpty && 
           !isTokenExpiring;
  }

  /// Gets the age of the session in milliseconds
  int get sessionAge {
    return DateTime.now().millisecondsSinceEpoch - createdAt;
  }

  /// Creates a new session from login response tokens
  factory AuthSessionModel.fromTokens({
    required String accessToken,
    required String refreshToken,
    String? email,
    bool twoFactorEnabled = false,
    bool biometricEnabled = false,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return AuthSessionModel(
      accessToken: accessToken,
      refreshToken: refreshToken,
      createdAt: now,
      lastUpdated: now,
      twoFactorEnabled: twoFactorEnabled,
      email: email,
      biometricEnabled: biometricEnabled,
    );
  }

  @override
  String toString() {
    return 'AuthSessionModel(email: $email, twoFactorEnabled: $twoFactorEnabled, '
        'biometricEnabled: $biometricEnabled, isValid: $isValid, '
        'isTokenExpiring: $isTokenExpiring)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthSessionModel &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.createdAt == createdAt &&
        other.lastUpdated == lastUpdated &&
        other.twoFactorEnabled == twoFactorEnabled &&
        other.email == email &&
        other.biometricEnabled == biometricEnabled;
  }

  @override
  int get hashCode {
    return Object.hash(
      accessToken,
      refreshToken,
      createdAt,
      lastUpdated,
      twoFactorEnabled,
      email,
      biometricEnabled,
    );
  }
}
