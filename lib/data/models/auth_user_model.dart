import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/auth_user.dart';

part 'auth_user_model.g.dart';

/// Data model for user information with JSON serialization
///
/// This model handles the conversion between API responses and domain entities.
/// It matches the structure returned by the tokenCheck() API endpoint.
@JsonSerializable()
class AuthUserModel {
  /// User's unique identifier
  @JsonKey(name: 'userID')
  final String id;

  /// User's full name
  final String name;

  /// User's email address
  final String email;

  /// Whether the user's email has been verified
  final bool emailVerified;

  /// User's phone number
  final String phone;

  /// Whether the user's phone number has been verified
  final bool phoneVerified;

  /// Timestamp of the last password change (in milliseconds since epoch as string)
  final String? lastPassChange;

  /// Timestamp of the last update (in milliseconds since epoch)
  final int? lastUpdate;

  /// Multi-factor authentication setting
  /// - "0" = disabled
  /// - "app" = app-based 2FA
  /// - "sms" = SMS-based 2FA
  final String? multiFactor;

  const AuthUserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.phone,
    required this.phoneVerified,
    this.lastPassChange,
    this.lastUpdate,
    this.multiFactor,
  });

  /// Creates an AuthUserModel from JSON
  factory AuthUserModel.fromJson(Map<String, dynamic> json) =>
      _$AuthUserModelFromJson(json);

  /// Converts AuthUserModel to JSON
  Map<String, dynamic> toJson() => _$AuthUserModelToJson(this);

  /// Converts this data model to a domain entity
  AuthUser toDomain() {
    return AuthUser(
      id: id,
      name: name,
      email: email,
      emailVerified: emailVerified,
      phone: phone,
      phoneVerified: phoneVerified,
      lastPassChange: lastPassChange,
      lastUpdate: lastUpdate,
      multiFactor: multiFactor,
    );
  }

  /// Creates an AuthUserModel from a domain entity
  factory AuthUserModel.fromDomain(AuthUser user) {
    return AuthUserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      phone: user.phone,
      phoneVerified: user.phoneVerified,
      lastPassChange: user.lastPassChange,
      lastUpdate: user.lastUpdate,
      multiFactor: user.multiFactor,
    );
  }

  @override
  String toString() {
    return 'AuthUserModel(id: $id, name: $name, email: $email, '
        'emailVerified: $emailVerified, phone: $phone, '
        'phoneVerified: $phoneVerified, multiFactor: $multiFactor)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthUserModel &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.emailVerified == emailVerified &&
        other.phone == phone &&
        other.phoneVerified == phoneVerified &&
        other.lastPassChange == lastPassChange &&
        other.lastUpdate == lastUpdate &&
        other.multiFactor == multiFactor;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      email,
      emailVerified,
      phone,
      phoneVerified,
      lastPassChange,
      lastUpdate,
      multiFactor,
    );
  }
}
